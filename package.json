{"name": "fme-start", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-router": "^1.127.1", "@tanstack/react-start": "^1.127.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "vite": "^7.0.4"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4"}}